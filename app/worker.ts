import { processTextWithLLM } from "./langchainUtils";
import { shuffleSentence } from "./utils"; // Import necessary functions

// Define the message types
interface ProcessSentenceMessage {
  type: 'processSentence';
  id: number; // To identify the sentence being processed
  text: string;
}

interface ProcessedSentenceResult {
  type: 'processedResult';
  id: number;
  hint?: string; // Optional hint
  translation?: string; // Placeholder for translation
  error?: string;
}

// Listen for messages from the main thread
self.onmessage = async (event: MessageEvent<ProcessSentenceMessage>) => {
  const { type, id, text } = event.data;
  console.log('Received message:', event.data);
  if (type === 'processSentence') {
    try {

      const task = `
          两个任务，
          1. 请将以下长句子切分成多个部分
            切分规则：
            - 根据句子的语法结构（如从句、连接词、并列结构）进行切分。
            - 目标是每个部分包含大约 6 到 10 个单词，但请根据句子实际情况灵活调整词数。
            以为数组的格式返回
          2. 将整个句子翻译成中文
          3. 你不需要做其他多余解释，只返回对应json就可以, 不需要携带markdown格式

          重要：如果句子太短不能切分句子的话，不需要做任何解释，返回这样的json就可以
          {
            "parts": [],
            "translation": "{翻译后的句子}"
          }

          返回格式：
          {
            "parts": [
              "part1",
              "part2",
              ...
            ],
            "translation": "翻译后的句子"
          }
          
        `;
      const llmResult = await processTextWithLLM({
        text: text,
        customTask: task,
      });
      const resobj = JSON.parse(llmResult) as { parts: string[], translation: string };
      const hint = resobj.parts.map((item, index) => `${index + 1}. ${shuffleSentence(item)}`).join('\r\n');
      const translation = resobj.translation;

      const result: ProcessedSentenceResult = {
        type: 'processedResult',
        id: id,
        hint: hint,
        translation: translation
      };

      // Post the result back to the main thread
      console.log('Posting result:', result);
      self.postMessage(result);

    } catch (error) {
      const err = error as Error;
      console.error(`Error processing sentence ${id}:`, err);
      const result: ProcessedSentenceResult = {
        type: 'processedResult',
        id: id,
        error: err.message || 'Unknown error',
      };
      self.postMessage(result);
    }
  }
};