import React from 'react';

interface TranslationDisplayProps {
  translation?: string;
  isVisible: boolean;
}

export default function TranslationDisplay({ translation, isVisible }: TranslationDisplayProps) {
  if (!isVisible || !translation) return null;

  return (
    <div className="mb-4 p-3 bg-purple-50 border border-purple-200 rounded-lg dark:bg-purple-900/30 dark:border-purple-800">
      <div className="flex items-start">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-purple-500 dark:text-purple-400 mr-2 mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 716.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
        </svg>
        <div className="flex-1">
          <h3 className="text-sm font-medium text-purple-800 dark:text-purple-200 mb-1">翻译</h3>
          <p className="text-sm text-purple-700 dark:text-purple-300">{translation}</p>
        </div>
      </div>
    </div>
  );
}