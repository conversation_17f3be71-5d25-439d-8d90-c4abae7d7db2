"use client"
import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface SettingsPanelProps {
  isVisible: boolean;
  onClose: () => void;
  showHint: boolean;
  onToggleHint: () => void;
  showTranslation: boolean;
  onToggleTranslation: () => void;
  difficultyMode: 'auto' | 'full';
  onChangeDifficultyMode: (mode: 'auto' | 'full') => void;
}

export default function SettingsPanel({
  isVisible,
  onClose,
  showHint,
  onToggleHint,
  showTranslation,
  onToggleTranslation,
  difficultyMode,
  onChangeDifficultyMode,
}: SettingsPanelProps) {
  return (
    <AnimatePresence>
      {isVisible && (
        <>
          {/* 背景遮罩 */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-40"
            onClick={onClose}
          />
          
          {/* 设置面板 */}
          <motion.div
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            transition={{ type: 'spring', damping: 25, stiffness: 200 }}
            className="fixed right-0 top-0 bottom-0 w-80 bg-white dark:bg-gray-800 shadow-xl z-50 overflow-y-auto"
          >
            <div className="p-6">
              {/* 标题栏 */}
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">设置</h2>
                <button
                  onClick={onClose}
                  className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {/* 设置项 */}
              <div className="space-y-6">
                {/* 显示提示 */}
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-sm font-medium text-gray-900 dark:text-white">显示提示</h3>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      启用后可以查看句子结构提示
                    </p>
                  </div>
                  <button
                    onClick={onToggleHint}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      showHint ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-700'
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        showHint ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>

                {/* 显示翻译 */}
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-sm font-medium text-gray-900 dark:text-white">显示翻译</h3>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      启用后会在界面上显示中文翻译
                    </p>
                  </div>
                  <button
                    onClick={onToggleTranslation}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      showTranslation ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-700'
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        showTranslation ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>

                {/* 难度模式 */}
                <div>
                  <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-3">难度模式</h3>
                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="difficultyMode"
                        value="auto"
                        checked={difficultyMode === 'auto'}
                        onChange={() => onChangeDifficultyMode('auto')}
                        className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                      />
                      <div className="ml-3">
                        <span className="text-sm text-gray-900 dark:text-white">智能模式</span>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          根据句子长度自动调整显示的单词数量
                        </p>
                      </div>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="difficultyMode"
                        value="full"
                        checked={difficultyMode === 'full'}
                        onChange={() => onChangeDifficultyMode('full')}
                        className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                      />
                      <div className="ml-3">
                        <span className="text-sm text-gray-900 dark:text-white">完整模式</span>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          显示句子中的所有单词
                        </p>
                      </div>
                    </label>
                  </div>
                </div>

                {/* 难度说明 */}
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">智能模式说明</h4>
                  <div className="space-y-1 text-xs text-gray-600 dark:text-gray-300">
                    <div className="flex justify-between">
                      <span>简单 (≤8词):</span>
                      <span>显示60%的单词</span>
                    </div>
                    <div className="flex justify-between">
                      <span>中等 (9-15词):</span>
                      <span>显示80%的单词</span>
                    </div>
                    <div className="flex justify-between">
                      <span>困难 (>15词):</span>
                      <span>显示所有单词</span>
                    </div>
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                    优先显示名词、动词、形容词和副词等重要词汇
                  </p>
                </div>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}
