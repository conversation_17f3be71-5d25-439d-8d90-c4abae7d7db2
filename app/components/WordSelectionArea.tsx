import React from 'react';
import { WordInfo } from '../types';

interface WordSelectionAreaProps {
  shuffledWords: WordInfo[];
  onSelectWord: (word: WordInfo, index: number) => void;
}

export default function WordSelectionArea({ shuffledWords, onSelectWord }: WordSelectionAreaProps) {
  return (
    <div className="w-full mb-4 overflow-hidden">
      <div className="flex flex-wrap justify-start content-start">
        {shuffledWords.map((item, idx) => (
          <span
            key={item.word + '-' + idx}
            className="border border-gray-300 rounded-lg cursor-pointer m-1 px-2 py-1 bg-gray-50 text-gray-800 hover:bg-gray-100 dark:bg-gray-800 dark:text-gray-200 dark:border-gray-700 dark:hover:bg-gray-700 flex flex-col items-center shadow-sm text-sm"
            onClick={() => onSelectWord(item, idx)}
          >
            <span className="font-medium">{item.word}</span>
            <span className="text-xs opacity-75 hidden sm:inline">{item.pos}</span>
          </span>
        ))}
      </div>
    </div>
  );
}