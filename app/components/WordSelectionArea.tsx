import React from 'react';
import { WordInfo } from '../types';
import { getWordsWithPos, shuffleArray } from '../utils';

interface WordSelectionAreaProps {
  shuffledWords: WordInfo[];
  onSelectWord: (word: WordInfo, index: number) => void;
  // 新增：分块显示相关属性
  allChunks?: string[];
  currentChunkIndex?: number;
  difficultyMode?: 'auto' | 'full';
}

export default function WordSelectionArea({
  shuffledWords,
  onSelectWord,
  allChunks = [],
  currentChunkIndex = 0,
  difficultyMode = 'full'
}: WordSelectionAreaProps) {

  // 如果是智能模式且有多个块，使用分块显示
  if (difficultyMode === 'auto' && allChunks.length > 1) {
    return (
      <div className="w-full mb-4 overflow-hidden">
        <div className="space-y-3">
          {allChunks.map((chunk, chunkIndex) => {
            const chunkWords = getWordsWithPos(chunk);
            const shuffledChunkWords = shuffleArray(chunkWords);
            const isCurrentChunk = chunkIndex === currentChunkIndex;
            const isCompletedChunk = chunkIndex < currentChunkIndex;

            return (
              <div key={chunkIndex} className="relative">
                {/* 块标题 */}
                <div className="flex items-center mb-2">
                  <span className={`text-xs font-medium px-2 py-1 rounded-full ${isCompletedChunk
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                      : isCurrentChunk
                        ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'
                        : 'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400'
                    }`}>
                    第 {chunkIndex + 1} 部分
                    {isCompletedChunk && ' ✓'}
                    {isCurrentChunk && ' (当前)'}
                  </span>
                </div>

                {/* 块的单词 */}
                <div className={`border rounded-lg p-3 ${isCurrentChunk
                    ? 'border-blue-300 bg-blue-50/30 dark:border-blue-800 dark:bg-blue-900/10'
                    : isCompletedChunk
                      ? 'border-green-300 bg-green-50/30 dark:border-green-800 dark:bg-green-900/10'
                      : 'border-gray-200 bg-gray-50/30 dark:border-gray-700 dark:bg-gray-800/10'
                  }`}>
                  <div className="flex flex-wrap justify-start content-start">
                    {shuffledChunkWords.map((item, idx) => {
                      // 找到这个单词在全局 shuffledWords 中的索引
                      const globalIndex = shuffledWords.findIndex(w =>
                        w.word === item.word && w.pos === item.pos
                      );

                      return (
                        <span
                          key={`${chunkIndex}-${item.word}-${idx}`}
                          className={`border rounded-lg m-1 px-2 py-1 flex flex-col items-center shadow-sm text-sm transition-all ${isCurrentChunk && globalIndex !== -1
                              ? 'border-gray-300 cursor-pointer bg-gray-50 text-gray-800 hover:bg-gray-100 dark:bg-gray-800 dark:text-gray-200 dark:border-gray-700 dark:hover:bg-gray-700'
                              : 'border-gray-200 bg-gray-100 text-gray-500 cursor-not-allowed dark:bg-gray-700 dark:text-gray-400 dark:border-gray-600'
                            }`}
                          onClick={isCurrentChunk && globalIndex !== -1 ? () => onSelectWord(item, globalIndex) : undefined}
                        >
                          <span className="font-medium">{item.word}</span>
                          <span className="text-xs opacity-75 hidden sm:inline">{item.pos}</span>
                        </span>
                      );
                    })}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  }

  // 传统的单一区域显示（完整模式或单块）
  return (
    <div className="w-full mb-4 overflow-hidden">
      <div className="flex flex-wrap justify-start content-start">
        {shuffledWords.map((item, idx) => (
          <span
            key={item.word + '-' + idx}
            className="border border-gray-300 rounded-lg cursor-pointer m-1 px-2 py-1 bg-gray-50 text-gray-800 hover:bg-gray-100 dark:bg-gray-800 dark:text-gray-200 dark:border-gray-700 dark:hover:bg-gray-700 flex flex-col items-center shadow-sm text-sm"
            onClick={() => onSelectWord(item, idx)}
          >
            <span className="font-medium">{item.word}</span>
            <span className="text-xs opacity-75 hidden sm:inline">{item.pos}</span>
          </span>
        ))}
      </div>
    </div>
  );
}