import React from 'react';
import { WordInfo } from '../types';

interface SentenceAreaProps {
  sentenceWords: WordInfo[];
  onRemoveWord: (word: WordInfo, index: number) => void;
}

export default function SentenceArea({ sentenceWords, onRemoveWord }: SentenceAreaProps) {
  return (
    <div className="sentence min-h-[80px] border border-gray-300 dark:border-gray-600 rounded-lg p-3 mb-4 flex flex-wrap items-center justify-center shadow-sm bg-white dark:bg-gray-800 transition-all">
      {sentenceWords.length > 0 ? (
        sentenceWords.map((item, idx) => (
          <span
            key={idx}
            className="border border-blue-300 rounded-lg cursor-pointer m-1 px-2 py-1 bg-blue-50 text-blue-800 hover:bg-blue-100 dark:bg-blue-900/30 dark:text-blue-200 dark:border-blue-800 dark:hover:bg-blue-900/50 flex flex-col items-center shadow-sm transition-all text-sm"
            onClick={() => onRemoveWord(item, idx)}
          >
            <span className="font-medium">{item.word}</span>
            <span className="text-xs opacity-75 hidden sm:inline">{item.pos}</span>
          </span>
        ))
      ) : (
        <p className="text-gray-400 dark:text-gray-500 text-center italic text-sm">点击下方单词组成句子</p>
      )}
    </div>
  );
}