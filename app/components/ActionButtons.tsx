import React from 'react';
import { motion } from 'framer-motion';

interface ActionButtonsProps {
  onCheckSentence: () => void;
  onGetHint: () => void;
  onToggleTranslation: () => void;
  onRestart: () => void;
  isConfirmDisabled: boolean;
  isHintDisabled: boolean;
  showTranslation: boolean;
}

export default function ActionButtons({
  onCheckSentence,
  onGetHint,
  onToggleTranslation,
  onRestart,
  isConfirmDisabled,
  isHintDisabled,
  showTranslation,
}: ActionButtonsProps) {
  return (
    <div className="fixed left-0 right-0 bottom-0 z-20 grid grid-cols-2 sm:grid-cols-4 gap-2 mt-auto pb-4">
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        className="px-2 py-2 rounded-lg bg-green-500 text-white hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed shadow-sm transition-all flex items-center justify-center text-sm"
        onClick={onCheckSentence}
        disabled={isConfirmDisabled}
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
        </svg>
        确认
      </motion.button>
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        className="px-2 py-2 rounded-lg bg-blue-500 text-white hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed shadow-sm transition-all flex items-center justify-center text-sm"
        onClick={onGetHint}
        disabled={isHintDisabled}
      >
        提示
      </motion.button>
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        className={`px-2 py-2 rounded-lg ${showTranslation ? 'bg-purple-500 hover:bg-purple-600' : 'bg-gray-500 hover:bg-gray-600'} text-white disabled:opacity-50 disabled:cursor-not-allowed shadow-sm transition-all flex items-center justify-center text-sm`}
        onClick={onToggleTranslation}
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 716.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
        </svg>
        {showTranslation ? '隐藏翻译' : '显示翻译'}
      </motion.button>
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        className="px-2 py-2 rounded-lg bg-red-500 text-white hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed shadow-sm transition-all flex items-center justify-center text-sm"
        onClick={onRestart}
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm2.958 14.767a7.002 7.002 0 0110.485-3.185 1 1 0 111.262 1.534 5.002 5.002 0 00-7.42 2.836L11 13a1 1 0 110 2h5a1 1 0 011 1v3a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.666-1.885z" clipRule="evenodd" />
        </svg>
        重来
      </motion.button>
    </div>
  );
}