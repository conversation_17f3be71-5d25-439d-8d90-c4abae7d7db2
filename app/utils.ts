import nlp from "compromise";
import { WordInfo } from "./types";


export interface Lyric {
  time: number;
  text: string;
}
export interface StructuredLyric {
  start: number;
  text: string;
  end: number;
}
export function parseLrcToJson(lrcContent: string): StructuredLyric[] {
  const lines = lrcContent.split('\n');
  const tempLyrics: Lyric[] = [];
  const lyricRegex = /\[(\d{2}):(\d{2})\.(\d{2,3})\](.*)/;
  let startParsing = false
  for (const line of lines) {
    if (line.includes('Lesson')) {
      startParsing = true
    }
    if (!startParsing) continue
    const match = line.match(lyricRegex);
    if (match) {
      const minutes = parseInt(match[1], 10);
      const seconds = parseInt(match[2], 10);
      const milliseconds = parseInt(match[3], 10); // Can be 2 or 3 digits
      const text = match[4].trim();
      // Convert time to milliseconds
      const timeInMs = (minutes * 60 + seconds) * 1000 + (match[3].length === 2 ? milliseconds * 10 : milliseconds);

      // Only include lines with actual text content
      if (text) {
        tempLyrics.push({
          time: timeInMs,
          text: text,
        });
      }
    }
  }

  // Now calculate end times and format the output
  const structuredLyrics: StructuredLyric[] = [];
  for (let i = 0; i < tempLyrics.length; i++) {
    const currentLyric = tempLyrics[i];
    const nextLyric = tempLyrics[i + 1];

    // The end time of the current lyric is the start time of the next lyric.
    // For the last lyric, we can set the end time to its start time,
    // or add a small duration if needed (though using the next timestamp is standard).
    const endTime = nextLyric ? nextLyric.time : currentLyric.time + 10000;

    structuredLyrics.push({
      start: currentLyric.time,
      text: currentLyric.text,
      end: endTime
    });
  }
  return structuredLyrics
}

export function getWords(sentence: string): string[] {
  return sentence.split(' ')
  // return nlp(sentence).terms().out('array');

}

/**
 * Shuffles the words in a sentence randomly.
 * @param sentence The input sentence string.
 * @returns The sentence with words shuffled.
 */
export function shuffleSentence(sentence: string): string {
  const words = getWords(sentence);

  // Fisher-Yates (Knuth) Shuffle Algorithm
  for (let i = words.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [words[i], words[j]] = [words[j], words[i]]; // Swap elements
  }

  return words.join(' ');
}

export function shuffleArray<T>(array: T[]): T[] {
  const newArray = [...array];
  for (let i = newArray.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
  }
  return newArray;
}

export const getWordsWithPos = (text: string): WordInfo[] => {
  const words = getWords(text);
  const doc = nlp(text);
  return words.map((word, index) => {
    const match = doc.match(word);
    const term = match?.terms()?.document?.[0][index].tags;
    const pos = term ? [...term].join(', ') : 'unknown';
    return { word, pos };
  });
};

// Difficulty grading system functions
export const getDifficultyLevel = (sentenceLength: number): 'easy' | 'medium' | 'hard' => {
  if (sentenceLength <= 8) return 'easy';
  if (sentenceLength <= 15) return 'medium';
  return 'hard';
};

export const getDisplayWords = (words: WordInfo[], difficulty: 'easy' | 'medium' | 'hard', fullMode: boolean): WordInfo[] => {
  if (fullMode) return words;

  const importantPos = ['Noun', 'Verb', 'Adjective', 'Adverb'];
  const importantWords = words.filter(word => importantPos.some(pos => word.pos.includes(pos)));
  const otherWords = words.filter(word => !importantPos.some(pos => word.pos.includes(pos)));

  let displayCount;
  switch (difficulty) {
    case 'easy':
      displayCount = Math.max(3, Math.ceil(words.length * 0.6));
      break;
    case 'medium':
      displayCount = Math.max(5, Math.ceil(words.length * 0.8));
      break;
    case 'hard':
    default:
      displayCount = words.length;
      break;
  }

  const wordsToDisplay = importantWords.slice(0, displayCount);
  const remainingCount = displayCount - wordsToDisplay.length;

  if (remainingCount > 0) {
    wordsToDisplay.push(...otherWords.slice(0, remainingCount));
  }

  return shuffleArray(wordsToDisplay);
};