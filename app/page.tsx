"use client"
import React, { useState, useEffect, useRef, useCallback } from "react";
import { parseLrcToJson } from "./utils";
import { shuffleArray, getWordsWithPos, getChunkDifficulty } from "./utils";
// import { motion, AnimatePresence } from "framer-motion";
import { ProcessedLyric, WordInfo } from "./types";

// Import the new components
import LoadingIndicator from './components/LoadingIndicator';
import ProgressBar from './components/ProgressBar';
import SentenceArea from './components/SentenceArea';
import WordSelectionArea from './components/WordSelectionArea';
import TranslationDisplay from './components/TranslationDisplay';
import ActionButtons from './components/ActionButtons';
import ToastNotification from './components/ToastNotification';
import SettingsPanel from './components/SettingsPanel';
import DifficultyIndicator from './components/DifficultyIndicator';

export default function Home() {
  const [lyrics, setLyrics] = useState<ProcessedLyric[]>([]);
  const [currentIdx, setCurrentIdx] = useState<number>(() => {
    if (typeof window !== 'undefined') {
      const savedIdx = localStorage.getItem('currentLyricIndex');
      return savedIdx ? parseInt(savedIdx, 10) : 0;
    }
    return 0;
  });
  const [currentText, setCurrentText] = useState<string>("");
  const [shuffledWords, setShuffledWords] = useState<WordInfo[]>([]);
  const [sentenceWords, setSentenceWords] = useState<WordInfo[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [currentChunkIndex, setCurrentChunkIndex] = useState<number>(0);
  const [currentChunkInfo, setCurrentChunkInfo] = useState<{
    currentChunk: string;
    totalChunks: number;
    isLastChunk: boolean;
    allChunks: string[];
  } | null>(null);
  // 新增：已完成的块状态
  const [completedChunks, setCompletedChunks] = useState<{
    [chunkIndex: number]: WordInfo[];
  }>({});
  const [showTranslation, setShowTranslation] = useState<boolean>(() => {
    if (typeof window !== 'undefined') {
      const savedSetting = localStorage.getItem('showTranslation');
      return savedSetting ? JSON.parse(savedSetting) : true;
    }
    return true;
  });
  const [showHint, setShowHint] = useState<boolean>(() => {
    if (typeof window !== 'undefined') {
      const savedSetting = localStorage.getItem('showHint');
      return savedSetting ? JSON.parse(savedSetting) : true;
    }
    return true;
  });
  const [difficultyMode, setDifficultyMode] = useState<'auto' | 'full'>(() => {
    if (typeof window !== 'undefined') {
      const savedMode = localStorage.getItem('difficultyMode');
      return savedMode === 'full' ? 'full' : 'auto';
    }
    return 'auto';
  });
  const [toast, setToast] = useState<{
    content: string;
    type: "hint" | "translation" | "success" | "error" | "info";
    visible: boolean;
  }>({ content: "", type: "info", visible: false });
  const [isSettingsOpen, setIsSettingsOpen] = useState<boolean>(false);

  const hideTimerRef = useRef<NodeJS.Timeout | null>(null);
  const workerRef = useRef<Worker | null>(null);

  // 设置当前块的辅助函数
  const setupCurrentChunk = useCallback((lyric: ProcessedLyric, chunkIndex: number = 0, resetCompleted: boolean = false) => {
    // 只有当大模型处理完成或者是完整模式时才设置
    if (difficultyMode === 'auto' && lyric.isProcessing) {
      // 如果还在处理中，暂时使用完整句子避免跳动
      const chunkInfo = {
        currentChunk: lyric.text,
        totalChunks: 1,
        isLastChunk: true,
        allChunks: [lyric.text]
      };

      setCurrentChunkInfo(chunkInfo);
      setCurrentText(lyric.text);
      setCurrentChunkIndex(0);

      if (resetCompleted) {
        setCompletedChunks({});
      }

      const wordsWithPos = getWordsWithPos(lyric.text);
      setShuffledWords(shuffleArray(wordsWithPos));
      setSentenceWords([]);
      return;
    }

    // 使用大模型切分的结果，如果没有则使用完整句子
    const parts = (difficultyMode === 'auto' && lyric.parts && lyric.parts.length > 0)
      ? lyric.parts
      : [lyric.text];

    const currentChunk = parts[chunkIndex] || parts[0];
    const chunkInfo = {
      currentChunk,
      totalChunks: parts.length,
      isLastChunk: chunkIndex >= parts.length - 1,
      allChunks: parts
    };

    setCurrentChunkInfo(chunkInfo);
    setCurrentText(currentChunk);
    setCurrentChunkIndex(chunkIndex);

    // 如果是重置（新句子），清空已完成的块
    if (resetCompleted) {
      setCompletedChunks({});
    }

    // 在分块模式下，shuffledWords 应该只包含当前块的单词
    // 但在新的UI设计中，WordSelectionArea 会自己处理所有块的显示
    const wordsWithPos = getWordsWithPos(currentChunk);
    setShuffledWords(shuffleArray(wordsWithPos));
    setSentenceWords([]);
  }, [difficultyMode]);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('showTranslation', JSON.stringify(showTranslation));
    }
  }, [showTranslation]);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('showHint', JSON.stringify(showHint));
    }
  }, [showHint]);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('difficultyMode', difficultyMode);
    }
  }, [difficultyMode]);

  useEffect(() => {
    workerRef.current = new Worker(new URL('./worker.ts', import.meta.url));

    workerRef.current.onmessage = (event: MessageEvent<{ type: string; id: number; parts?: string[]; translation?: string; error?: string }>) => {
      const { type, id, parts, translation } = event.data;
      if (type === 'processedResult') {
        setLyrics(prevLyrics => {
          const newLyrics = [...prevLyrics];
          const lyricIndex = newLyrics.findIndex(lyric => lyric.start === id);
          if (lyricIndex !== -1) {
            const updatedLyric = {
              ...newLyrics[lyricIndex],
              parts: parts,
              translation: translation,
              isProcessing: false,
            };
            newLyrics[lyricIndex] = updatedLyric;

            // 如果更新的是当前正在显示的句子，重新设置显示
            if (lyricIndex === currentIdx && difficultyMode === 'auto') {
              setupCurrentChunk(updatedLyric, 0, true);
            }

            if (typeof window !== 'undefined') {
              localStorage.setItem('processedLyrics', JSON.stringify(newLyrics));
            }
          }
          return newLyrics;
        });
      }
    };

    workerRef.current.onerror = (error) => {
      console.error("Worker error:", error);
    };

    const loadLyrics = async () => {
      setLoading(true);
      if (typeof window !== 'undefined') {
        const savedLyrics = localStorage.getItem('processedLyrics');
        if (savedLyrics) {
          const parsedLyrics: ProcessedLyric[] = JSON.parse(savedLyrics);
          setLyrics(parsedLyrics);
          const initialSentence = parsedLyrics[currentIdx] || parsedLyrics[0];
          if (initialSentence) {
            setupCurrentChunk(initialSentence, 0, true);
            if (!parsedLyrics[currentIdx]) setCurrentIdx(0);
          }
          setLoading(false);
          return;
        }
      }

      const res = await fetch("/lyrics/18－Electric Currents in Modern Art.lrc");
      const lrc = await res.text();
      const json = parseLrcToJson(lrc);

      if (json.length > 0) {
        const initialLyrics: ProcessedLyric[] = json.map(lyric => ({
          ...lyric,
          isProcessing: true,
        }));
        setLyrics(initialLyrics);

        initialLyrics.forEach((lyric) => {
          workerRef.current?.postMessage({
            type: 'processSentence',
            id: lyric.start,
            text: lyric.text,
          });
        });

        const initialSentence = initialLyrics[currentIdx] || initialLyrics[0];
        if (initialSentence) {
          setupCurrentChunk(initialSentence, 0, true);
          if (!initialLyrics[currentIdx]) setCurrentIdx(0);
        }
      }
      setLoading(false);
    }

    loadLyrics();

    return () => {
      workerRef.current?.terminate();
    };
  }, [currentIdx, difficultyMode, setupCurrentChunk]); // Add setupCurrentChunk to dependencies

  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('currentLyricIndex', currentIdx.toString());
    }

    if (lyrics.length > 0 && currentIdx < lyrics.length) {
      const nextSentence = lyrics[currentIdx];
      setupCurrentChunk(nextSentence, 0, true); // 重置到第一个块，清空已完成的块
      setCurrentChunkIndex(0);
    } else if (currentIdx >= lyrics.length && lyrics.length > 0) {
      setShuffledWords([]);
      setSentenceWords([]);
      setCurrentChunkInfo(null);
      setCurrentChunkIndex(0);
      setCompletedChunks({});
      if (typeof window !== 'undefined') {
        localStorage.removeItem('currentLyricIndex');
        localStorage.removeItem('processedLyrics');
      }
    }
  }, [currentIdx, lyrics, setupCurrentChunk]);

  const showToast = (content: string, type: "hint" | "translation" | "success" | "error" | "info") => {
    if (hideTimerRef.current) {
      clearTimeout(hideTimerRef.current);
      hideTimerRef.current = null;
    }

    setToast({
      content,
      type,
      visible: true
    });

    hideTimerRef.current = setTimeout(() => {
      setToast(prev => ({ ...prev, visible: false }));
      hideTimerRef.current = null;
    }, 3000);
  };

  const checkSentence = () => {
    const formedSentence = sentenceWords.map(item => item.word).join(' ');
    if (formedSentence === currentText) {
      showToast("正确！", "success");

      // 保存当前块的完成状态
      setCompletedChunks(prev => ({
        ...prev,
        [currentChunkIndex]: [...sentenceWords]
      }));

      // 检查是否还有更多块需要完成
      if (currentChunkInfo && !currentChunkInfo.isLastChunk) {
        // 移动到下一个块
        const nextChunkIndex = currentChunkIndex + 1;
        setupCurrentChunk(lyrics[currentIdx], nextChunkIndex, false);
        showToast(`完成第 ${currentChunkIndex + 1}/${currentChunkInfo.totalChunks} 部分`, "info");
      } else {
        // 完成整个句子，移动到下一句
        setCurrentIdx(currentIdx + 1);
        setCurrentChunkIndex(0);
        showToast("句子完成！", "success");
      }
    } else {
      showToast("错误，请重试。", "error");
      setShuffledWords([...shuffledWords, ...sentenceWords]);
      setSentenceWords([]);
    }
  };

  const clickWord = async (wordInfo: WordInfo, idx: number) => {
    setSentenceWords([...sentenceWords, wordInfo]);
    setShuffledWords(shuffledWords.filter((_, i) => i !== idx));

    if ('speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(wordInfo.word);
      window.speechSynthesis.speak(utterance);
    }
  }

  const removeWordFromSentence = (wordInfo: WordInfo, idx: number) => {
    const newSentenceWords = sentenceWords.filter((_, i) => i !== idx);
    setSentenceWords(newSentenceWords);
    setShuffledWords([...shuffledWords, wordInfo]);
  }

  const handleGetHint = () => {
    const currentLyric = lyrics[currentIdx];
    if (currentLyric?.isProcessing) {
      showToast("句子分析中...", "info");
      return;
    }

    if (currentChunkInfo && currentChunkInfo.allChunks.length > 1) {
      // 显示所有块的结构提示
      const hintText = currentChunkInfo.allChunks
        .map((chunk, index) => `${index + 1}. ${chunk}`)
        .join('\n');
      showToast(`句子结构提示：\n${hintText}`, "hint");
    } else {
      showToast("当前句子无需切分或分析中。", "info");
    }
  };

  // 修改翻译按钮功能，改为切换翻译显示状态
  const handleToggleTranslation = () => {
    setShowTranslation(!showTranslation);
  };

  const handleRestart = () => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('currentLyricIndex');
      localStorage.removeItem('processedLyrics');
    }
    setCurrentIdx(0);
    setSentenceWords([]);
    setShuffledWords([]);
    setCompletedChunks({});
    setCurrentChunkIndex(0);
  };

  // const handleRestartCurrentSentence = () => {
  //   if (lyrics.length > 0 && currentIdx < lyrics.length) {
  //     const currentSentence = lyrics[currentIdx];
  //     setupCurrentChunk(currentSentence, 0, true);
  //     setCurrentChunkIndex(0);
  //     showToast("重新开始当前句子", "info");
  //   }
  // };

  const handleOpenSettings = () => {
    setIsSettingsOpen(true);
  };

  const handleCloseSettings = () => {
    setIsSettingsOpen(false);
  };

  const handleToggleHint = () => {
    setShowHint(!showHint);
  };

  const handleChangeDifficultyMode = (mode: 'auto' | 'full') => {
    setDifficultyMode(mode);
    // 重新计算当前句子的块显示
    if (lyrics.length > 0 && currentIdx < lyrics.length) {
      const currentSentence = lyrics[currentIdx];
      setupCurrentChunk(currentSentence, 0, true); // 重置到第一个块，清空已完成的块
      setCurrentChunkIndex(0);
    }
  };

  return (
    <div className="w-full max-w-2xl mx-auto px-3 py-4 min-h-screen flex flex-col relative">
      {loading ? (
        <LoadingIndicator />
      ) : (
        <div className="flex-1 flex flex-col pb-[72px]">
          <ProgressBar current={currentIdx} total={lyrics.length} />

          {/* 难度指示器和块进度 */}
          {lyrics[currentIdx] && currentChunkInfo && (
            <div className="mb-3 flex flex-col items-center space-y-2">
              <DifficultyIndicator
                difficulty={getChunkDifficulty(currentText)}
                wordCount={getWordsWithPos(currentText).length}
                displayedWordCount={shuffledWords.length + sentenceWords.length}
              />
              {currentChunkInfo.totalChunks > 1 && (
                <div className="text-xs text-gray-500 dark:text-gray-400">
                  块进度: {currentChunkIndex + 1}/{currentChunkInfo.totalChunks}
                  {difficultyMode === 'auto' && (
                    <span className="ml-2 text-blue-600 dark:text-blue-400">
                      (智能切分)
                    </span>
                  )}
                </div>
              )}
            </div>
          )}

          <SentenceArea
            sentenceWords={sentenceWords}
            onRemoveWord={removeWordFromSentence}
            completedChunks={completedChunks}
            currentChunkIndex={currentChunkIndex}
            totalChunks={currentChunkInfo?.totalChunks || 1}
          />
          <WordSelectionArea
            shuffledWords={shuffledWords}
            onSelectWord={clickWord}
            allChunks={currentChunkInfo?.allChunks || []}
            currentChunkIndex={currentChunkIndex}
            difficultyMode={difficultyMode}
          />
          <TranslationDisplay
            isVisible={showTranslation}
            translation={lyrics[currentIdx]?.translation}
          />
          <ActionButtons
            onCheckSentence={checkSentence}
            onGetHint={handleGetHint}
            onToggleTranslation={handleToggleTranslation}
            onRestart={handleRestart}
            onOpenSettings={handleOpenSettings}
            isConfirmDisabled={sentenceWords.length === 0}
            isHintDisabled={(loading || !currentText || lyrics[currentIdx]?.isProcessing) ?? false}
            showTranslation={showTranslation}
            showHint={showHint}
          />
        </div>
      )}
      <ToastNotification {...toast} />

      {/* 设置面板 */}
      <SettingsPanel
        isVisible={isSettingsOpen}
        onClose={handleCloseSettings}
        showHint={showHint}
        onToggleHint={handleToggleHint}
        showTranslation={showTranslation}
        onToggleTranslation={handleToggleTranslation}
        difficultyMode={difficultyMode}
        onChangeDifficultyMode={handleChangeDifficultyMode}
      />
    </div>
  );
}
