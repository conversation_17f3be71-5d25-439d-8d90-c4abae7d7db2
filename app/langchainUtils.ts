import { ChatOpenAI } from "@langchain/openai";
import { PromptTemplate } from "@langchain/core/prompts";

/**
 * 使用 LangChain 处理文本的通用函数 (使用 LCEL pipe)
 * @param text 需要处理的文本
 * @param llm 可选的 LLM 实例。如果未提供，将使用默认的 OpenAI 模型。
 * @returns LLM 处理后的结果
 */
export async function processTextWithLLM(
  {
    customTask,
    text
  }: {
    customTask?: string,
    text: string
  }
): Promise<string> {
  const model = new ChatOpenAI({
    apiKey: process.env.NEXT_PUBLIC_OPENAI_API_KEY,
    configuration: {
      baseURL: 'https://api.siliconflow.cn/v1',
    },
    modelName: 'Qwen/Qwen2.5-72B-Instruct',
  });
  // 定义一个灵活的 Prompt 模板
  // 您可以根据需要修改这个模板，例如用于总结、翻译、问答等
  const prompt = PromptTemplate.fromTemplate(
    `请根据以下文本完成任务：

文本: {inputText}

任务: {task}
`
  );

  // 定义任务，这里只是一个示例，您可以根据实际用途修改
  const task = customTask || "总结这段文本的主要内容。"; // 默认任务

  // 使用 LCEL 构建链：将 prompt 和 model 通过 pipe 连接
  const chain = prompt.pipe(model);

  // 运行链并获取结果
  // invoke 方法现在直接接受模板变量作为输入
  const result = await chain.invoke({ inputText: text, task: task });

  // invoke 返回的是一个对象，包含 content 属性 (对于新的模型接口)
  // 或者 text 属性 (对于旧的接口或某些模型)
  // 为了兼容性，我们尝试获取 content，如果不存在则获取 text
  // 将 AIMessage 类型的返回值转换为字符串
  return typeof result.content === 'string'
    ? result.content
    : Array.isArray(result.content)
      ? result.content.map(item => typeof item === 'string' ? item : JSON.stringify(item)).join('')
      : JSON.stringify(result.content);
}
